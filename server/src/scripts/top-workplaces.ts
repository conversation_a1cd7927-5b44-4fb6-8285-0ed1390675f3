const getWorkplacesShiftData = async () => {
  const map = new Map();
  let shouldContinue = true;
  let url = "http://localhost:3000/shifts";
  while (shouldContinue) {
    const resp = await fetch(url);
    if (!resp.ok) {
      throw new Error(`Failed to fetch shifts: ${resp.status}`);
    }
    const shiftsJson = await resp.json();

    for (const shift of shiftsJson.data) {
      if (shift.workerId && !shift.cancelledAt) {
        map.set(shift.workplaceId, (map.get(shift.workplaceId) || 0) + 1);
      }
    }

    if (!shiftsJson?.links?.next) {
      shouldContinue = false;
    } else {
      url = shiftsJson.links.next;
    }
  }

  const sortedWorkplaces = [...map.entries()].sort((a, b) => b[1] - a[1]);
  return sortedWorkplaces;
};

const fetchMostActiveWorkplaces = async () => {
  const sortedWorkplaces = await getWorkplacesShiftData();
  const topWorkplaces: Array<{
    name: string;
    shifts: number;
  }> = [];

  for (const [workplaceId, shiftCount] of sortedWorkplaces) {
    if (topWorkplaces.length >= 3) break;

    const resp = await fetch(`http://localhost:3000/workplaces/${workplaceId}`);
    if (!resp.ok) {
      throw new Error(`Failed to fetch workplace: ${resp.status}`);
    }

    const workplaceJson = await resp.json();
    const workplace = workplaceJson.data;
    if (workplace.status === 0) {
      topWorkplaces.push({
        name: workplace.name,
        shifts: shiftCount,
      });
    }
  }
  return topWorkplaces;
};

fetchMostActiveWorkplaces();
